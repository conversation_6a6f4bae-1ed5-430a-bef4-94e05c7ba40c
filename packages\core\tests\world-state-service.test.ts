/**
 * WorldStateService 重构后的测试用例
 * 验证智能用户筛选机制和性能优化
 */

import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import { Context } from 'koishi';
import { WorldStateService } from '../world-state-service';
import { HistoryConfig } from '../config';
import { EntityType } from '../../memory';

describe('WorldStateService 重构验证', () => {
    let ctx: Context;
    let worldStateService: WorldStateService;
    let mockConfig: HistoryConfig;

    beforeEach(() => {
        // 模拟配置
        mockConfig = {
            advanced: {
                maxMessages: 100,
                maxSegments: 50
            },
            summarizationPrompt: 'Test prompt'
        } as HistoryConfig;

        // 模拟 Context
        ctx = {
            database: {
                get: vi.fn(),
                create: vi.fn(),
                set: vi.fn()
            },
            logger: vi.fn(() => ({
                info: vi.fn(),
                warn: vi.fn(),
                error: vi.fn(),
                debug: vi.fn()
            }))
        } as any;

        worldStateService = new WorldStateService(ctx, mockConfig);
    });

    afterEach(() => {
        vi.clearAllMocks();
    });

    describe('智能用户筛选机制', () => {
        it('应该正确提取@提及的用户', () => {
            const content = 'Hello @user1 and @user2, how are you?';
            const mentions = worldStateService['extractMentionedUsers'](content);
            
            expect(mentions).toEqual(['user1', 'user2']);
        });

        it('应该正确处理空消息列表', async () => {
            const result = await worldStateService.recallForContext([]);
            expect(result).toEqual([]);
        });

        it('应该优先返回直接参与者', async () => {
            const mockMessages = [
                {
                    id: 'msg-1',
                    content: 'Hello world',
                    sender: { id: 'user-1', name: 'User 1' },
                    timestamp: new Date(),
                    date: '2024-01-01',
                    time: '10:00:00',
                    quoted: false
                },
                {
                    id: 'msg-2',
                    content: 'Hi there',
                    sender: { id: 'user-2', name: 'User 2' },
                    timestamp: new Date(),
                    date: '2024-01-01',
                    time: '10:01:00',
                    quoted: false
                }
            ];

            // 模拟内存服务返回空结果
            const mockMemoryService = {
                searchFacts: vi.fn().mockResolvedValue({ success: true, data: [] }),
                searchUserProfiles: vi.fn().mockResolvedValue({ success: true, data: [] })
            };
            worldStateService['memoryService'] = mockMemoryService as any;

            // 模拟数据库返回空结果
            ctx.database.get = vi.fn().mockResolvedValue([]);

            const result = await worldStateService.recallForContext(mockMessages);
            
            // 应该包含所有直接参与者
            expect(result).toContain('user-1');
            expect(result).toContain('user-2');
        });

        it('应该正确处理@提及的用户', async () => {
            const mockMessages = [
                {
                    id: 'msg-1',
                    content: 'Hey @user3, check this out!',
                    sender: { id: 'user-1', name: 'User 1' },
                    timestamp: new Date(),
                    date: '2024-01-01',
                    time: '10:00:00',
                    quoted: false
                }
            ];

            // 模拟内存服务和数据库
            const mockMemoryService = {
                searchFacts: vi.fn().mockResolvedValue({ success: true, data: [] }),
                searchUserProfiles: vi.fn().mockResolvedValue({ success: true, data: [] })
            };
            worldStateService['memoryService'] = mockMemoryService as any;
            ctx.database.get = vi.fn().mockResolvedValue([]);

            const result = await worldStateService.recallForContext(mockMessages);
            
            // 应该包含直接参与者和被@的用户
            expect(result).toContain('user-1'); // 直接参与者
            expect(result).toContain('user3'); // 被@的用户
        });

        it('应该限制返回的用户数量', async () => {
            const mockMessages = Array.from({ length: 20 }, (_, i) => ({
                id: `msg-${i}`,
                content: `Message ${i}`,
                sender: { id: `user-${i}`, name: `User ${i}` },
                timestamp: new Date(),
                date: '2024-01-01',
                time: '10:00:00',
                quoted: false
            }));

            // 模拟内存服务和数据库
            const mockMemoryService = {
                searchFacts: vi.fn().mockResolvedValue({ success: true, data: [] }),
                searchUserProfiles: vi.fn().mockResolvedValue({ success: true, data: [] })
            };
            worldStateService['memoryService'] = mockMemoryService as any;
            ctx.database.get = vi.fn().mockResolvedValue([]);

            const result = await worldStateService.recallForContext(mockMessages);
            
            // 应该限制在最大用户数量内（默认8个）
            expect(result.length).toBeLessThanOrEqual(8);
        });
    });

    describe('语义相关用户查找', () => {
        it('应该正确处理内存服务错误', async () => {
            const mockMessages = [
                {
                    id: 'msg-1',
                    content: 'Test message',
                    sender: { id: 'user-1', name: 'User 1' },
                    timestamp: new Date(),
                    date: '2024-01-01',
                    time: '10:00:00',
                    quoted: false
                }
            ];

            // 模拟内存服务抛出错误
            const mockMemoryService = {
                searchFacts: vi.fn().mockRejectedValue(new Error('Memory service error')),
                searchUserProfiles: vi.fn().mockRejectedValue(new Error('Memory service error'))
            };
            worldStateService['memoryService'] = mockMemoryService as any;

            const result = await worldStateService['findSemanticRelevantUsers'](mockMessages, 5);
            
            // 应该返回空数组而不是抛出错误
            expect(result).toEqual([]);
        });
    });

    describe('基于姓名的用户查找', () => {
        it('应该正确匹配用户名', async () => {
            const mockMessages = [
                {
                    id: 'msg-1',
                    content: 'Alice is working on the project with Bob',
                    sender: { id: 'user-1', name: 'User 1' },
                    timestamp: new Date(),
                    date: '2024-01-01',
                    time: '10:00:00',
                    quoted: false
                }
            ];

            // 模拟数据库返回用户实体
            const mockEntities = [
                {
                    id: 'entity-alice',
                    name: 'Alice',
                    type: EntityType.Person,
                    isDeleted: false
                },
                {
                    id: 'entity-bob',
                    name: 'Bob',
                    type: EntityType.Person,
                    isDeleted: false
                },
                {
                    id: 'entity-charlie',
                    name: 'Charlie',
                    type: EntityType.Person,
                    isDeleted: false
                }
            ];

            ctx.database.get = vi.fn().mockResolvedValue(mockEntities);

            const result = await worldStateService['findNamedUsers'](mockMessages);
            
            // 应该找到Alice和Bob，但不包括Charlie
            expect(result.some(user => user.userId === 'entity-alice')).toBe(true);
            expect(result.some(user => user.userId === 'entity-bob')).toBe(true);
            expect(result.some(user => user.userId === 'entity-charlie')).toBe(false);
        });

        it('应该正确处理数据库错误', async () => {
            const mockMessages = [
                {
                    id: 'msg-1',
                    content: 'Test message',
                    sender: { id: 'user-1', name: 'User 1' },
                    timestamp: new Date(),
                    date: '2024-01-01',
                    time: '10:00:00',
                    quoted: false
                }
            ];

            // 模拟数据库抛出错误
            ctx.database.get = vi.fn().mockRejectedValue(new Error('Database error'));

            const result = await worldStateService['findNamedUsers'](mockMessages);
            
            // 应该返回空数组而不是抛出错误
            expect(result).toEqual([]);
        });
    });

    describe('缓存机制', () => {
        it('应该正确设置和获取缓存', () => {
            const mockEntity = {
                id: 'entity-1',
                name: 'Test User',
                type: EntityType.Person
            } as any;

            // 设置缓存
            worldStateService['setCachedUserEntity']('user-1', mockEntity);

            // 获取缓存
            const cached = worldStateService['getCachedUserEntity']('user-1');
            expect(cached).toEqual(mockEntity);
        });

        it('应该正确处理过期缓存', () => {
            const mockEntity = {
                id: 'entity-1',
                name: 'Test User',
                type: EntityType.Person
            } as any;

            // 手动设置过期的缓存
            worldStateService['userEntityCache'].set('user-1', {
                entity: mockEntity,
                timestamp: Date.now() - 10 * 60 * 1000 // 10分钟前
            });

            // 获取缓存应该返回null（已过期）
            const cached = worldStateService['getCachedUserEntity']('user-1');
            expect(cached).toBeNull();
        });
    });
});
